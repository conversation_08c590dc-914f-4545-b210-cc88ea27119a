_target_: nuplan.planning.scenario_builder.scenario_filter.ScenarioFilter
_convert_: 'all'

scenario_types: null                # List of scenario types to include
scenario_tokens: null               # List of scenario tokens to include

log_names: null                     # Filter scenarios by log names
map_names: null                     # Filter scenarios by map names

num_scenarios_per_type: null        # Number of scenarios per type
limit_total_scenarios: 1000000         # Limit total scenarios (float = fraction, int = num) - this filter can be applied on top of num_scenarios_per_type
timestamp_threshold_s: null         # Filter scenarios to ensure scenarios have more than `timestamp_threshold_s` seconds between their initial lidar timestamps
ego_displacement_minimum_m: null    # Whether to remove scenarios where the ego moves less than a certain amount
ego_start_speed_threshold: null     # Limit to scenarios where the ego reaches a certain speed from below
ego_stop_speed_threshold: null      # Limit to scenarios where the ego reaches a certain speed from above
speed_noise_tolerance: null         # Value at or below which a speed change between two timepoints should be ignored as noise.

expand_scenarios: true              # Whether to expand multi-sample scenarios to multiple single-sample scenarios
remove_invalid_goals: true          # Whether to remove scenarios where the mission goal is invalid
shuffle: false                      # Whether to shuffle the scenarios
