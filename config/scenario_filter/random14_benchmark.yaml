_target_: nuplan.planning.scenario_builder.scenario_filter.ScenarioFilter
_convert_: all
scenario_types:
  - starting_left_turn
  - starting_right_turn
  - starting_straight_traffic_light_intersection_traversal
  - stopping_with_lead
  - high_lateral_acceleration
  - high_magnitude_speed
  - low_magnitude_speed
  - traversing_pickup_dropoff
  - waiting_for_pedestrian_to_cross
  - behind_long_vehicle
  - stationary_in_traffic
  - near_multiple_vehicles
  - changing_lane
  - following_lane_with_lead

scenario_tokens:
  - "09ca86cc9ae65428"
  - d77598de12fb5f46
  - "011118ec4f9952bc"
  - e9faae87fb83540d
  - "7b28cbcdcae35e7e"
  - "80c56ad735545de3"
  - "9031cf5175f253cf"
  - "22022119f3bd53f3"
  - ac67e458c88d5d78
  - "9cab2c90252c549b"
  - a639bd510b0d5b0a
  - "9e507708119c5596"
  - "4f9321ffbcb95b55"
  - a0f50ac13caa51ac
  - "843eb1eee80b529e"
  - "5051ccd75314515c"
  - "1939feda817551d5"
  - "281b1fe38bdc5467"
  - c9d8621d7c2255e6
  - "8b8cdc059d585494"
  - "6c6ddf6740d355ef"
  - "74e550e274f959a4"
  - "0393fbf49cd55295"
  - f00be015e344557c
  - c00c5e8e89e3571c
  - "33ef46dbce1c577f"
  - "69b83d7c62d65006"
  - "7cc26b3af3b0557f"
  - dd117e2e2dfa560d
  - "7280988648d05358"
  - "9a81702c38d757b8"
  - a88d3779eb535274
  - "27fb96d75d4759a2"
  - "5dec4c8bfd49502f"
  - "942f6862e6055f62"
  - "72038bf480a55042"
  - e3342e2e8b535405
  - "07874767a6d55ad7"
  - f1cd02f2371c5c67
  - "2e56997063c057a0"
  - "382951755d8e5e77"
  - "2f69576b55305cc8"
  - f9e6e7fc604b5ff9
  - "24cc8621c8595a92"
  - "0c88901b6cfb53d0"
  - "93c583b46398560e"
  - "7835a781f6bd5688"
  - "4aeaa8bebf7a57f1"
  - "33624e1585945551"
  - e5d69d5a0b135831
  - "711c54430cae590f"
  - "29b77c53a46956af"
  - "7d094d765a8f5ffa"
  - a1d2f54d8ec1564c
  - af3681f002005504
  - a26d34c327365e71
  - "15a3339778d058db"
  - "81ecd0ddcc4e5c1f"
  - "770a7cb9f1585e38"
  - "7f50df6eea5c54e9"
  - b7485487d49853ca
  - cb153e6cd19851ba
  - "4e0c8ec081805052"
  - b1f9cfc8ac885d91
  - "4eaa9a0e22c95491"
  - "812679cd23f45d5d"
  - "7be9d726f1be5003"
  - dc98be51b7155407
  - aff9a658a9e450cb
  - "845645e7c5665590"
  - d3dfcbb4c7be5e8b
  - "33a3eb36641857e6"
  - a489a4faafbd5c67
  - "1ce0f41f0d9754f5"
  - "2df19b9bf2fe5c88"
  - "0908906ecbed58df"
  - d7a3ae0854575652
  - "29a854ae1aee58ff"
  - "64986687478f5338"
  - cada319ebf2b505a
  - a874cefaca8e5b69
  - "5e33493023b75f33"
  - "356d80474d1252c0"
  - "0eb1eb9046925cf8"
  - bc2485198080598a
  - b342971fb07451f9
  - "098144ba356652a5"
  - "33b5947ae3d75a80"
  - b086f30875e65add
  - b0f8ae874f2b5e78
  - "38e301b3cbad5707"
  - e5ef6199dc3b5909
  - "4236a1f3c9e35f9d"
  - ccdca0ce28565318
  - "2664bb314a155875"
  - d8633b984d75530e
  - d21b22d6c0405ad7
  - f3895453b6c35e51
  - dca16f92a1015a7f
  - "9fd5ec2b453d556e"
  - "619533b026ce59f1"
  - d5a09eb525e8592a
  - "48b0081cd2385220"
  - "35dd043cd64e5537"
  - cc4bc91994df5424
  - "522cda9d26b1526a"
  - "8160c5b3a5c9555e"
  - "704c05ebeb5959e6"
  - "78c5b673402b5861"
  - "34a52b2f68f357df"
  - a614ad720a76576c
  - b6f238f681bf5a09
  - b3b422a58ff35545
  - f0be7cf0e03e58db
  - c1027e45d3b956e4
  - "99c06a975b465903"
  - e51649556f215dd9
  - b526edea27bc5f93
  - e9d360ea046554d6
  - e7fc2f835ea95ece
  - "2fd784c6bf4f572c"
  - "28d5868f9d6e5035"
  - "6c37bc8ac424562b"
  - "2f8c75dfca3f50b9"
  - ead9590e094d5a8f
  - "5af50221d55658e4"
  - "65af9ce73917587a"
  - "0cc3d9bb137b5a2b"
  - "7e4a2d822a2e55e0"
  - bd5783916d995801
  - fa10175cddfa52fa
  - e095899901a75a56
  - "936b015ebca15109"
  - "2f149371d38e5806"
  - "40bcab2def635436"
  - a438e0b5c26351dd
  - ee479ca0620c5077
  - c3bdac749eeb5206
  - "3c0e98e1b77b5d44"
  - c28d662b14b05c83
  - "2be476b6b8db5693"
  - "2574c2dbef5850f1"
  - "144473d8f03a57f4"
  - bff3663d750c5ee1
  - "541899555b645329"
  - "0d05468e475657b8"
  - "9eb227aec6dc5e23"
  - "8fd7ccd629615f24"
  - ef202cd018ce55e1
  - "7dea428154ad5d8a"
  - "6582d5ea9e2e5fa8"
  - "1f5dc80046035f11"
  - "1a278d6ef4435e69"
  - "52e1da615adf57c4"
  - "0612d791f0825acd"
  - "88c66c9df39e5193"
  - "9bb232eead8e525d"
  - db0204e9603f59e9
  - da64279ff31552d1
  - "462120b01b425182"
  - e257ace5273058ae
  - "660d375c109f5eed"
  - "9bdc15b26c455633"
  - c7be4ae92b455fa3
  - "84008abb23955152"
  - "957e6a7dec135b62"
  - "29686b1e8e6859ca"
  - "56726e1f05ae5164"
  - b85af6bc62cf5e03
  - "341ca59d11c15742"
  - eaef31406a205542
  - "63e48fa44d7c5aa4"
  - "713972db4cf35d09"
  - "3452d2d6fb655c2b"
  - "8efa8475e103598e"
  - e5fd3465d00b57ae
  - "8de10fd86b825304"
  - "477820688c4f5683"
  - "8ec9c713f4fc5d52"
  - "9fb968b3cea6507a"
  - a762a67013d4550e
  - "714dffb0115b5071"
  - "17e261bca6435550"
  - "561e8b2703f651f5"
  - "96074214b9645952"
  - "094db8d50cf95250"
  - "61d9e27ce6ee5a38"
  - daaaa1360bcf5451
  - c167ed0e96e455c4
  - "724cc15d7fc856df"
  - c700656703ec57bc
  - d6dde3e83ebe5b53
  - fa75094937835cf0
  - adc1c85043525786
  - e4bac3a9053955e2
  - "2fa51997ae9554cb"
  - "3ee6e16b65f956a8"
  - "1039c939079950f4"
  - "2e82153ebbed5d67"
  - bb0e6c0c719a5e7f
  - "7dc66aff7ac85f5f"
  - fd31b918c10b56e2
  - "0fa049c07b5b57c0"
  - bac6f1145eef5bf2
  - "6a1ffac82183523e"
  - "9f006885686852e7"
  - "3acc225a64965bd8"
  - a2ec5056da3c5c67
  - f69d0b59d08d512b
  - "3474e3d6c7485935"
  - c139820a97455f83
  - "93b3c95f3fea5812"
  - a09bce6cb8eb591b
  - c4f2b1f170eb5649
  - "9ba0cad0c3e7580f"
  - "1772467d58bb5dec"
  - "07db0457ad745d8e"
  - "94ce7eff6722572c"
  - b83ee0e949e45520
  - b497992774675304
  - "255e819738da54df"
  - "1b1aafb5916e5534"
  - "93e79e172de3521b"
  - c757a4faf12f52be
  - "155505762e1d5cc0"
  - "51933e2f44775d6a"
  - "071b206ea836546e"
  - f5d7ec43b9415862
  - "0ef918a26914564e"
  - "1c8be883a97a575e"
  - bd345bdaa3715b71
  - c998657ba1535677
  - c1c5dec6bab3598a
  - "7721f05072a85928"
  - "95495f60a1b659f3"
  - "1173af87b1e551ce"
  - "399986448051533a"
  - c299dae1f0745da8
  - beb18cddda575028
  - fcedb0da569c518b
  - a88f9e60e43c5ebc
  - f5a6ac1890c75f87
  - e7aa83d247dd5cdc
  - cfbbb77238bd541f
  - "62db31b428315ae8"
  - "0409c3925f245965"
  - "70086024fed658cf"
  - ceec28e1943f5d76
  - "3094b2c29265536a"
log_names: null
map_names: null
num_scenarios_per_type: null
limit_total_scenarios: null
timestamp_threshold_s: 15
ego_displacement_minimum_m: null
ego_start_speed_threshold: null
ego_stop_speed_threshold: null
speed_noise_tolerance: null
expand_scenarios: null
remove_invalid_goals: true
shuffle: false
