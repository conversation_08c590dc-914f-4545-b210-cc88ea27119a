hydra:
  run:
    dir: ${output_dir}
  output_subdir: ${output_dir}/code/hydra                       # Store hydra's config breakdown here for debugging
  searchpath:                                                   # Only <exp_dir> in these paths are discoverable
    - pkg://nuplan.planning.script.config.common
    - pkg://nuplan.planning.script.config.simulation
    - pkg://nuplan.planning.script.experiments                  # Put experiments configs in script/experiments/<exp_dir>
    - config/simulation
    - config/scenario_filter

defaults:
  # Add ungrouped items
  - default_experiment
  - default_common
  - default_submission

  - simulation_metric:
      - default_metrics
  - callback:
      - simulation_log_callback
  - main_callback:
      - time_callback
      - metric_file_callback
      - metric_aggregator_callback
      - metric_summary_callback
  - splitter: nuplan

  # Hyperparameters need to be specified
  - observation: null
  - ego_controller: null
  - planner: null
  - simulation_time_controller: step_simulation_time_controller
  - metric_aggregator:
      - default_weighted_average

  - override hydra/job_logging: none                            # Disable hydra's logging
  - override hydra/hydra_logging: none                          # Disable hydra's logging

experiment_name: 'simulation'
aggregated_metric_folder_name: 'aggregator_metric'         # Aggregated metric folder name
aggregator_save_path: ${output_dir}/${aggregated_metric_folder_name}


# Progress Visualization
enable_simulation_progress_bar: true # Show for every simulation its progress

# Simulation Setup
simulation_history_buffer_duration: 2.0  # [s] The look back duration to initialize the simulation history buffer with

# Number (or fractional, e.g., 0.25) of GPUs available for single simulation (per scenario and planner).
# This number can also be < 1 because we allow multiple models to be loaded into a single GPU.
# In case this number is 0 or null, no GPU is used for simulation and all cpu cores are leveraged
# Note, that the user have to make sure that if a number < 1 is chosen, the model will fit 1 / num_gpus into GPU memory
number_of_gpus_allocated_per_simulation: 1

# This number specifies number of CPU threads that are used for simulation
# In case this is null, then each simulation will use unlimited resources.
# That will typically swamp the host computer, leading to slowdowns and failure.
number_of_cpus_allocated_per_simulation: 1

# Set false to disable metric computation
run_metric: true

# Set to rerun metrics with existing simulation logs without setting run_metric to false.
simulation_log_main_path: null

# If false, continue running the simulation even it a scenario has failed
exit_on_failure: false

# Maximum number of workers to be used for running simulation callbacks outside the main process
max_callback_workers: 4

# Disable callback parallelization when using the Sequential worker. By default, when running with the sequential worker,
# on_simulation_end callbacks are not submitted to a parallel worker.
disable_callback_parallelization: true

# Distributed processing mode. If multi-node simulation is enable, this parameter selects how the scenarios distributed
# to each node. The modes are:
#  - SCENARIO_BASED: Works in two stages, first getting a list of all, scenarios to process, then breaking up that
#                    list and distributing across the workers
#  - LOG_FILE_BASED: Works in a single stage, breaking up the scenarios based on what log file they are in and
#                    distributing the number of log files evenly across all workers
#  - SINGLE_NODE: Does no distribution, processes all scenarios in config
distributed_mode: 'SINGLE_NODE'