hydra:
  run:
    dir: ${output_dir}
  output_subdir: ${output_dir}/code/hydra           # Store hydra's config breakdown here for debugging
  searchpath:                                       # Only <exp_dir> in these paths are discoverable
    - pkg://nuplan.planning.script.config.common
    - pkg://nuplan.planning.script.config.training
    - pkg://nuplan.planning.script.experiments      # Put experiments configs in script/experiments/<exp_dir>
    - config/training


defaults:
  - default_experiment
  - default_common

  # Trainer and callbacks
  - lightning: custom_lightning
  - callbacks: default_callbacks

  # Optimizer settings
  - optimizer: adam  # [adam, adamw] supported optimizers
  - lr_scheduler: null  # [one_cycle_lr] supported lr_schedulers
  - warm_up_lr_scheduler: null  # [linear_warm_up, constant_warm_up] supported warm up lr schedulers

  # Data Loading
  - data_loader: default_data_loader
  - splitter: ???

  # Objectives and metrics
  - objective:
  - training_metric:
  - data_augmentation: null
  - data_augmentation_scheduler: null  # [default_augmentation_schedulers, stepwise_augmentation_probability_scheduler, stepwise_noise_parameter_scheduler] supported data augmentation schedulers
  - scenario_type_weights: default_scenario_type_weights
  - custom_trainer: null

nuplan_trainer: false
experiment_name: 'training'
objective_aggregate_mode: ??? # How to aggregate multiple objectives, can be 'mean', 'max', 'sum'

# Cache parameters
cache:
  cache_path:                                         # Local/remote path to store all preprocessed artifacts from the data pipeline
  use_cache_without_dataset: false                    # Load all existing features from a local/remote cache without loading the dataset
  force_feature_computation: false                    # Recompute features even if a cache exists
  cleanup_cache: false                                # Cleanup cached data in the cache_path, this ensures that new data are generated if the same cache_path is passed

# Mandatory parameters
py_func: ???                                          # Function to be run inside main (can be "train", "test", "cache")
epochs: 25
warmup_epochs: 3
lr: 1e-3
weight_decay: 0.0001
checkpoint:

# wandb settings
wandb:
  mode: disable
  project: nuplan-pluto
  name: ${experiment_name}
  log_model: all
  artifact:
  run_id:
